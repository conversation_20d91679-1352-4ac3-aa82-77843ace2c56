/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/logs/route";
exports.ids = ["app/api/logs/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flogs%2Froute&page=%2Fapi%2Flogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flogs%2Froute&page=%2Fapi%2Flogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_logs_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/logs/route.ts */ \"(rsc)/./src/app/api/logs/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/logs/route\",\n        pathname: \"/api/logs\",\n        filename: \"route\",\n        bundlePath: \"app/api/logs/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\logs\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_logs_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flogs%2Froute&page=%2Fapi%2Flogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/logs/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/logs/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\nconst DEFAULT_PAGE_SIZE = 20;\nconst VALID_SORT_COLUMNS = [\n    'request_timestamp',\n    'status_code',\n    'llm_provider_name',\n    'llm_model_name',\n    'llm_provider_latency_ms',\n    'processing_duration_ms',\n    'tokens_prompt',\n    'tokens_completion',\n    'custom_api_config_id',\n    'role_used'\n]; // Use const assertion for stricter enum type\n// Zod schema for query parameters\nconst LogsQuerySchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    page: zod__WEBPACK_IMPORTED_MODULE_2__.z.coerce.number().int().positive().optional().default(1),\n    pageSize: zod__WEBPACK_IMPORTED_MODULE_2__.z.coerce.number().int().positive().optional().default(DEFAULT_PAGE_SIZE),\n    // Ensure incoming string is parsed to Date object, then validated\n    startDate: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().datetime({\n        offset: true\n    }).optional().transform((val)=>val ? new Date(val) : undefined),\n    endDate: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().datetime({\n        offset: true\n    }).optional().transform((val)=>val ? new Date(val) : undefined),\n    customApiConfigId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().uuid().optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        'success',\n        'error'\n    ]).optional(),\n    sortBy: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"](VALID_SORT_COLUMNS).optional().default('request_timestamp'),\n    sortOrder: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        'asc',\n        'desc'\n    ]).optional().default('desc')\n});\nasync function GET(request) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n        if (!supabase) {\n            console.error('Supabase client could not be initialized in /api/logs.');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server configuration error.'\n            }, {\n                status: 500\n            });\n        }\n        // Check authentication\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            console.error('Authentication failed in /api/logs:', authError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized: You must be logged in to view logs.'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const queryParams = Object.fromEntries(searchParams.entries());\n        const validationResult = LogsQuerySchema.safeParse(queryParams);\n        if (!validationResult.success) {\n            console.error('Invalid query parameters for /api/logs:', validationResult.error.flatten());\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid query parameters',\n                issues: validationResult.error.flatten()\n            }, {\n                status: 400\n            });\n        }\n        const { page, pageSize, startDate, endDate, customApiConfigId, status, sortBy, sortOrder } = validationResult.data;\n        let query = supabase.from('request_logs').select('*', {\n            count: 'exact'\n        }).eq('user_id', user.id); // Explicitly filter by authenticated user\n        // Apply filters\n        if (startDate) {\n            query = query.gte('request_timestamp', startDate.toISOString());\n        }\n        if (endDate) {\n            // To make endDate inclusive for the day, set time to end of day\n            const inclusiveEndDate = new Date(endDate);\n            inclusiveEndDate.setUTCHours(23, 59, 59, 999); // Use UTC for consistency with ISO strings\n            query = query.lte('request_timestamp', inclusiveEndDate.toISOString());\n        }\n        if (customApiConfigId) {\n            query = query.eq('custom_api_config_id', customApiConfigId);\n        }\n        if (status) {\n            if (status === 'success') {\n                query = query.gte('status_code', 200).lt('status_code', 300);\n            } else if (status === 'error') {\n                query = query.gte('status_code', 400);\n            }\n        }\n        // Apply sorting\n        query = query.order(sortBy, {\n            ascending: sortOrder === 'asc'\n        });\n        const offset = (page - 1) * pageSize;\n        query = query.range(offset, offset + pageSize - 1);\n        const { data: logs, error, count } = await query;\n        if (error) {\n            console.error('Error fetching logs from Supabase:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch logs',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            logs,\n            pagination: {\n                currentPage: page,\n                pageSize,\n                totalCount: count || 0,\n                totalPages: Math.ceil((count || 0) / pageSize)\n            },\n            sorting: {\n                sortBy,\n                sortOrder\n            }\n        });\n        // Add cache headers for better performance\n        response.headers.set('Cache-Control', 'private, max-age=30, stale-while-revalidate=60');\n        return response;\n    } catch (e) {\n        console.error('Unexpected error in /api/logs GET handler:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected server error occurred.',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/logs/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flogs%2Froute&page=%2Fapi%2Flogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();