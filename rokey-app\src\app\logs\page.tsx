'use client';

import { useState, useEffect, use<PERSON><PERSON>back, ChangeEvent, FormEvent } from 'react';
import {
  AdjustmentsHorizontalIcon, CalendarDaysIcon, DocumentMagnifyingGlassIcon,
  EyeIcon, ArrowPathIcon, ArrowUpIcon, ArrowDownIcon, ArrowsUpDownIcon,
  FunnelIcon, ClockIcon, DocumentTextIcon
} from '@heroicons/react/24/outline';
import LogDetailModal from '@/components/logs/LogDetailModal';
import Button from '@/components/ui/Button';
import Card, { CardHeader, CardContent } from '@/components/ui/Card';
import Input, { Select } from '@/components/ui/Input';
import { LoadingTable } from '@/components/ui/LoadingSpinner';
import { transformRoleUsed, getRoleUsedBadgeClass, formatProviderName, formatModelName } from '@/utils/logFormatting';
import { useSubscription } from '@/hooks/useSubscription';

// Types from MyModelsPage - assuming they might be moved to a shared types file later
interface CustomApiConfigMini {
  id: string;
  name: string;
}

// Define types for Log entry and Pagination
interface RequestLog {
  id: string;
  request_timestamp: string;
  custom_api_config_id: string | null;
  custom_api_config_name?: string; // Will be populated client-side after fetching configs
  role_requested: string | null;
  role_used: string | null;
  llm_provider_name: string | null;
  llm_model_name: string | null;
  status_code: number | null;
  llm_provider_latency_ms: number | null;
  processing_duration_ms: number | null;
  input_tokens: number | null;
  output_tokens: number | null;
  cost: number | null;
  is_multimodal: boolean | null;
  ip_address: string | null;
  user_id: string | null;
  error_message: string | null;
  error_source: string | null;
  error_details_zod: string | null;
  llm_provider_status_code: number | null;
  request_payload_summary: any | null;
  response_payload_summary: any | null;
}

interface PaginationState {
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

interface FiltersState {
  startDate: string;
  endDate: string;
  customApiConfigId: string;
  status: string;
}

interface SortState {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

// Define which columns are sortable and map display name to field name
const sortableColumns: { label: string; field: string; defaultSortOrder?: 'asc' | 'desc' }[] = [
  { label: 'Timestamp', field: 'request_timestamp', defaultSortOrder: 'desc' },
  { label: 'API Model', field: 'custom_api_config_id' }, // Sorting by name would require join or client-side sort on resolved names
  { label: 'Role Used', field: 'role_used' },
  { label: 'Provider', field: 'llm_provider_name' },
  { label: 'LLM Model', field: 'llm_model_name' },
  { label: 'Status', field: 'status_code' },
  { label: 'Latency (LLM)', field: 'llm_provider_latency_ms' },
  { label: 'Latency (RoKey)', field: 'processing_duration_ms' },
  { label: 'Input Tokens', field: 'input_tokens' },
  { label: 'Output Tokens', field: 'output_tokens' },
];

const DEFAULT_PAGE_SIZE = 10; // Reduced for faster initial load

export default function LogsPage() {
  const { user } = useSubscription();
  const [logs, setLogs] = useState<RequestLog[]>([]);
  const [pagination, setPagination] = useState<PaginationState | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoadingConfigs, setIsLoadingConfigs] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [apiConfigs, setApiConfigs] = useState<CustomApiConfigMini[]>([]);

  const initialFilters: FiltersState = {
    startDate: '',
    endDate: '',
    customApiConfigId: 'all',
    status: 'all',
  };
  const [filters, setFilters] = useState<FiltersState>(initialFilters);
  const [showFilters, setShowFilters] = useState<boolean>(false);

  const [customConfigNameMap, setCustomConfigNameMap] = useState<Record<string, string>>({});

  // State for Log Detail Modal
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedLog, setSelectedLog] = useState<RequestLog | null>(null);

  const [sort, setSort] = useState<SortState>({ sortBy: 'request_timestamp', sortOrder: 'desc' });

  const fetchApiConfigs = async () => {
    setIsLoadingConfigs(true);
    try {
      const response = await fetch('/api/custom-configs');
      if (!response.ok) {
        throw new Error('Failed to fetch API model configurations');
      }
      const data: CustomApiConfigMini[] = await response.json();
      setApiConfigs(data);
      const nameMap: Record<string, string> = {};
      data.forEach(config => { nameMap[config.id] = config.name; });
      setCustomConfigNameMap(nameMap);
    } catch (err: any) {
      setError(`Error fetching configurations: ${err.message}`);
    } finally {
      setIsLoadingConfigs(false);
    }
  };

  const fetchLogs = useCallback(async (page = 1, currentFilters: FiltersState, currentSort: SortState) => {
    setIsLoading(true);
    setError(null);
    try {
      const params: Record<string, string> = {
        page: page.toString(),
        pageSize: DEFAULT_PAGE_SIZE.toString(),
        sortBy: currentSort.sortBy,
        sortOrder: currentSort.sortOrder,
      };
      if (currentFilters.startDate) params.startDate = new Date(currentFilters.startDate).toISOString();
      if (currentFilters.endDate) params.endDate = new Date(currentFilters.endDate).toISOString();
      if (currentFilters.customApiConfigId !== 'all') params.customApiConfigId = currentFilters.customApiConfigId;
      if (currentFilters.status !== 'all') params.status = currentFilters.status;
      
      const response = await fetch(`/api/logs?${new URLSearchParams(params).toString()}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.details || 'Failed to fetch logs');
      }
      const data = await response.json();
      setLogs(data.logs || []);
      setPagination(data.pagination || null);
    } catch (err: any) {
      setError(err.message);
      setLogs([]);
      setPagination(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    // Only fetch data when user is authenticated
    if (user) {
      // Delay initial data fetching to improve perceived performance
      const timer = setTimeout(() => {
        fetchApiConfigs();
        fetchLogs(1, filters, sort);
      }, 100);

      return () => clearTimeout(timer);
    } else if (user === null) {
      // User is explicitly null (not authenticated), stop loading
      setIsLoading(false);
      setIsLoadingConfigs(false);
    }
    // If user is undefined, we're still loading auth state, keep loading
  }, [fetchLogs, filters, sort, user]); // Add user dependency

  const handleFilterChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFilters(prev => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleApplyFilters = (e?: FormEvent<HTMLFormElement>) => {
    e?.preventDefault();
    fetchLogs(1, filters, sort);
  };

  const handleResetFilters = () => {
    setFilters(initialFilters);
    const defaultSort = { sortBy: 'request_timestamp', sortOrder: 'desc' } as SortState;
    setSort(defaultSort);
    fetchLogs(1, initialFilters, defaultSort);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && (!pagination || newPage <= pagination.totalPages)) {
      fetchLogs(newPage, filters, sort);
    }
  };

  const handleSort = (field: string) => {
    const newSortOrder = (sort.sortBy === field && sort.sortOrder === 'asc') ? 'desc' : 'asc';
    const newSortState: SortState = { sortBy: field, sortOrder: newSortOrder };
    setSort(newSortState);
    fetchLogs(1, filters, newSortState);
  };

  // Handlers for Log Detail Modal
  const handleOpenModal = (log: RequestLog) => {
    setSelectedLog(log);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedLog(null);
  };

  const getStatusClass = (statusCode: number | null) => {
    if (statusCode === null) return 'bg-gray-600 text-gray-100';
    if (statusCode >= 200 && statusCode < 300) return 'bg-green-600 text-green-100';
    if (statusCode >= 400) return 'bg-red-600 text-red-100';
    return 'bg-yellow-500 text-yellow-100'; // For 3xx or other statuses
  };

  const SortableHeader: React.FC<{ column: typeof sortableColumns[0] }> = ({ column }) => {
    const isCurrentSortColumn = sort.sortBy === column.field;
    return (
      <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
        <button
          onClick={() => handleSort(column.field)}
          className="flex items-center space-x-2 hover:text-white transition-colors duration-200 group"
        >
          <span>{column.label}</span>
          {isCurrentSortColumn ? (
            sort.sortOrder === 'asc' ?
              <ArrowUpIcon className="h-4 w-4 text-cyan-400" /> :
              <ArrowDownIcon className="h-4 w-4 text-cyan-400" />
          ) : (
            <ArrowsUpDownIcon className="h-4 w-4 text-gray-500 group-hover:text-gray-300" />
          )}
        </button>
      </th>
    );
  };

  return (
    <div className="min-h-screen w-full bg-[#040716] text-white overflow-x-hidden">
      {/* Header Section - Following analytics design */}
      <div className="border-b border-gray-800/50">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <h1 className="text-2xl font-semibold text-white">Request Logs</h1>
              <div className="flex items-center space-x-1">
                <button className="px-3 py-1 text-sm text-gray-400 hover:text-white transition-colors">
                  Real-time
                </button>
                <button className="px-3 py-1 text-sm bg-cyan-500 text-white rounded">
                  History
                </button>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`px-4 py-2 rounded-lg border transition-all duration-200 flex items-center space-x-2 ${
                  showFilters
                    ? 'bg-cyan-500 text-white border-cyan-500'
                    : 'bg-gray-900/50 text-gray-300 border-gray-700 hover:border-gray-600'
                }`}
              >
                <FunnelIcon className="h-4 w-4" />
                <span>{showFilters ? 'Hide Filters' : 'Show Filters'}</span>
              </button>
            </div>
          </div>

          {/* Welcome Message with Gradient */}
          <div className="mt-6">
            <h2 className="text-lg mb-2">
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400">
                Monitor and analyze your API request history
              </span>
            </h2>
            <p className="text-gray-400 text-sm">
              Track performance, debug issues, and optimize your routing configurations
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        {/* Error Message */}
        {error && (
          <div className="bg-red-900/20 border border-red-800 rounded-lg p-6 mb-8">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <p className="text-red-400">{error}</p>
            </div>
          </div>
        )}

        {/* Filters */}
        {showFilters && (
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 mb-8 animate-scale-in">
            <div className="mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold text-white">Filter Logs</h3>
                  <p className="text-gray-400 mt-1">Narrow down your search results</p>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <ClockIcon className="h-4 w-4" />
                  <span>Real-time updates</span>
                </div>
              </div>
            </div>
            <form onSubmit={handleApplyFilters} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    <CalendarDaysIcon className="h-4 w-4 inline mr-1" />
                    Start Date
                  </label>
                  <input
                    type="date"
                    name="startDate"
                    value={filters.startDate}
                    onChange={handleFilterChange}
                    className="w-full px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    <CalendarDaysIcon className="h-4 w-4 inline mr-1" />
                    End Date
                  </label>
                  <input
                    type="date"
                    name="endDate"
                    value={filters.endDate}
                    onChange={handleFilterChange}
                    className="w-full px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    <AdjustmentsHorizontalIcon className="h-4 w-4 inline mr-1" />
                    Configuration
                  </label>
                  <select
                    name="customApiConfigId"
                    value={filters.customApiConfigId}
                    onChange={handleFilterChange}
                    disabled={isLoadingConfigs}
                    className="w-full px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  >
                    <option value="all">All Configurations</option>
                    {apiConfigs.map(config => (
                      <option key={config.id} value={config.id}>{config.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    <DocumentMagnifyingGlassIcon className="h-4 w-4 inline mr-1" />
                    Status
                  </label>
                  <select
                    name="status"
                    value={filters.status}
                    onChange={handleFilterChange}
                    className="w-full px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  >
                    <option value="all">All Statuses</option>
                    <option value="success">Success (2xx)</option>
                    <option value="error">Error (4xx/5xx)</option>
                  </select>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setFilters(initialFilters);
                    fetchLogs(1, initialFilters, sort);
                  }}
                  className="px-4 py-2 bg-gray-800/50 text-gray-300 border border-gray-700 rounded-lg hover:border-gray-600 transition-all duration-200 flex items-center space-x-2"
                >
                  <ArrowPathIcon className="h-4 w-4" />
                  <span>Clear Filters</span>
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-cyan-500 text-white rounded-lg hover:bg-cyan-600 transition-all duration-200 flex items-center space-x-2"
                >
                  <DocumentMagnifyingGlassIcon className="h-4 w-4" />
                  <span>Apply Filters</span>
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-8 border border-gray-800/50">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-800 rounded w-1/4"></div>
              <div className="space-y-3">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="grid grid-cols-11 gap-4">
                    {[...Array(11)].map((_, j) => (
                      <div key={j} className="h-4 bg-gray-800 rounded"></div>
                    ))}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!isLoading && !logs.length && !error && (
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-12 border border-gray-800/50 text-center">
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 bg-gray-800/50 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-gray-700">
                <DocumentTextIcon className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">No Logs Found</h3>
              <p className="text-gray-400 mb-6">
                No request logs match your criteria. Once you make requests to the unified API, they will appear here.
              </p>
            </div>
          </div>
        )}

        {/* Logs Table */}
        {!isLoading && logs.length > 0 && (
          <>
            <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full text-sm">
                  <thead className="bg-gray-800/50 border-b border-gray-700">
                    <tr>
                      {sortableColumns.map(col => <SortableHeader key={col.field} column={col} />)}
                      <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                        Multimodal
                      </th>
                      <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700 bg-gray-900/30">
                    {logs.map((log, index) => (
                      <tr
                        key={log.id}
                        className="hover:bg-gray-800/30 transition-colors duration-200 animate-slide-in"
                        style={{ animationDelay: `${index * 50}ms` }}
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-white">
                          <div className="flex items-center space-x-2">
                            <ClockIcon className="h-4 w-4 text-gray-400" />
                            <span>{new Date(log.request_timestamp).toLocaleString()}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-white">
                          <div className="font-medium">
                            {log.custom_api_config_id ? (customConfigNameMap[log.custom_api_config_id] || log.custom_api_config_id.substring(0,8) + '...') : 'N/A'}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-white">
                          {(() => {
                            const roleInfo = transformRoleUsed(log.role_used);
                            return (
                              <span className={getRoleUsedBadgeClass(roleInfo.type)}>
                                {roleInfo.text}
                              </span>
                            );
                          })()}
                        </td>
                        <td className="px-6 py-4 text-white">
                          <span className="font-medium">{formatProviderName(log.llm_provider_name)}</span>
                        </td>
                        <td className="px-6 py-4 text-white truncate max-w-xs" title={formatModelName(log.llm_model_name)}>
                          <span className="font-medium">{formatModelName(log.llm_model_name)}</span>
                        </td>
                        <td className="px-6 py-4">
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusClass(log.status_code)}`}>
                            {log.status_code || 'N/A'}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-right text-white">
                          {log.llm_provider_latency_ms !== null ? `${log.llm_provider_latency_ms} ms` : '-'}
                        </td>
                        <td className="px-6 py-4 text-right text-white">
                          {log.processing_duration_ms !== null ? `${log.processing_duration_ms} ms` : '-'}
                        </td>
                        <td className="px-6 py-4 text-right text-white">
                          {log.input_tokens !== null ? log.input_tokens.toLocaleString() : '-'}
                        </td>
                        <td className="px-6 py-4 text-right text-white">
                          {log.output_tokens !== null ? log.output_tokens.toLocaleString() : '-'}
                        </td>
                        <td className="px-6 py-4 text-center">
                          {log.is_multimodal ? (
                            <span className="w-2 h-2 bg-green-500 rounded-full inline-block"></span>
                          ) : (
                            <span className="w-2 h-2 bg-gray-500 rounded-full inline-block"></span>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          <button
                            onClick={() => handleOpenModal(log)}
                            className="p-2 text-gray-400 hover:text-cyan-400 hover:bg-gray-800/50 rounded-lg transition-all duration-200"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          </div>

            {/* Pagination Controls */}
            {pagination && pagination.totalPages > 1 && (
              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-4 border border-gray-800/50 mt-6">
                <div className="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
                  <div className="text-sm text-gray-400">
                    Showing <span className="font-medium text-white">{(pagination.currentPage - 1) * pagination.pageSize + 1}</span>
                    {logs.length > 0 ? ` - ${Math.min(pagination.currentPage * pagination.pageSize, pagination.totalCount)}` : ''}
                    {' '}of <span className="font-medium text-white">{pagination.totalCount}</span> logs
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handlePageChange(pagination.currentPage - 1)}
                      disabled={pagination.currentPage <= 1 || isLoading}
                      className="px-3 py-1 text-sm bg-gray-800/50 text-gray-300 border border-gray-700 rounded hover:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      Previous
                    </button>
                    <span className="px-3 py-1 text-sm text-gray-400">
                      Page {pagination.currentPage} of {pagination.totalPages}
                    </span>
                    <button
                      onClick={() => handlePageChange(pagination.currentPage + 1)}
                      disabled={pagination.currentPage >= pagination.totalPages || isLoading}
                      className="px-3 py-1 text-sm bg-gray-800/50 text-gray-300 border border-gray-700 rounded hover:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}

        {/* Log Detail Modal */}
        {isModalOpen && selectedLog && (
          <LogDetailModal
            log={selectedLog}
            onClose={handleCloseModal}
            apiConfigNameMap={customConfigNameMap}
          />
        )}
      </div>
    </div>
  );
} 