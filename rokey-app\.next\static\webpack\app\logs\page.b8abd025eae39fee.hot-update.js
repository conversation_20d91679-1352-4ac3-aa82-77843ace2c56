"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/logs/page",{

/***/ "(app-pages-browser)/./src/utils/logFormatting.ts":
/*!************************************!*\
  !*** ./src/utils/logFormatting.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatModelName: () => (/* binding */ formatModelName),\n/* harmony export */   formatProviderName: () => (/* binding */ formatProviderName),\n/* harmony export */   formatRoleName: () => (/* binding */ formatRoleName),\n/* harmony export */   generateRoleUsedMessage: () => (/* binding */ generateRoleUsedMessage),\n/* harmony export */   getRoleUsedBadgeClass: () => (/* binding */ getRoleUsedBadgeClass),\n/* harmony export */   transformRoleUsed: () => (/* binding */ transformRoleUsed)\n/* harmony export */ });\n/**\n * Utility functions for transforming debug-style log messages into production-ready, user-friendly text\n */ /**\n * Check if a string looks like a role name (vs a technical debug pattern)\n */ const isLikelyRoleName = (str)=>{\n    // Exclude obvious technical patterns\n    const technicalPatterns = [\n        /default_key/i,\n        /attempt_\\d+/i,\n        /status_\\d+/i,\n        /failed/i,\n        /success/i,\n        /complexity_rr/i,\n        /fallback_position/i,\n        /^[a-f0-9-]{8,}/i,\n        /_then_/i,\n        /classification_/i,\n        /no_prompt/i,\n        /error/i\n    ];\n    // If it matches any technical pattern, it's not a role name\n    if (technicalPatterns.some((pattern)=>pattern.test(str))) {\n        return false;\n    }\n    // If it's a simple word or snake_case without numbers/technical terms, likely a role\n    return /^[a-z_]+$/i.test(str) && str.length > 2 && str.length < 50;\n};\n/**\n * Transform debug-style role_used messages into user-friendly text\n */ const transformRoleUsed = (roleUsed)=>{\n    if (!roleUsed) return {\n        text: 'N/A',\n        type: 'fallback'\n    };\n    // Handle simple role names first (clean role names without technical patterns)\n    if (isLikelyRoleName(roleUsed)) {\n        return {\n            text: formatRoleName(roleUsed),\n            type: 'role',\n            details: \"Role-based routing: \".concat(formatRoleName(roleUsed))\n        };\n    }\n    // Handle default key success patterns\n    if (roleUsed.includes('default_key') && roleUsed.includes('success')) {\n        const attemptMatch = roleUsed.match(/attempt_(\\d+)/);\n        const attempt = attemptMatch ? parseInt(attemptMatch[1]) : 1;\n        return {\n            text: attempt === 1 ? 'Default Key' : \"Default Key (Attempt \".concat(attempt, \")\"),\n            type: 'success',\n            details: attempt > 1 ? \"Required \".concat(attempt, \" attempts to succeed\") : undefined\n        };\n    }\n    // Handle default key failure patterns\n    if (roleUsed.includes('default_key') && roleUsed.includes('failed')) {\n        const attemptMatch = roleUsed.match(/attempt_(\\d+)/);\n        const statusMatch = roleUsed.match(/status_(\\w+)/);\n        const attempt = attemptMatch ? parseInt(attemptMatch[1]) : 1;\n        const status = statusMatch ? statusMatch[1] : 'unknown';\n        return {\n            text: \"Failed (Attempt \".concat(attempt, \")\"),\n            type: 'error',\n            details: \"Failed with status: \".concat(status)\n        };\n    }\n    // Handle multiple attempts failed\n    if (roleUsed.includes('default_all') && roleUsed.includes('attempts_failed')) {\n        const countMatch = roleUsed.match(/default_all_(\\d+)_attempts/);\n        const count = countMatch ? parseInt(countMatch[1]) : 0;\n        return {\n            text: \"All Keys Failed (\".concat(count, \" attempts)\"),\n            type: 'error',\n            details: \"Tried \".concat(count, \" different API keys, all failed\")\n        };\n    }\n    // Handle enhanced complexity-based routing with proximal search details\n    if (roleUsed.includes('complexity_rr_clsf_') || roleUsed.includes('complexity_level_')) {\n        // Enhanced pattern: complexity_rr_clsf_3_used_lvl_4_key_selected\n        const enhancedMatch = roleUsed.match(/complexity_rr_clsf_(\\d+)_used_lvl_(\\d+)/);\n        if (enhancedMatch) {\n            const classifiedLevel = enhancedMatch[1];\n            const usedLevel = enhancedMatch[2];\n            if (classifiedLevel === usedLevel) {\n                return {\n                    text: \"Complexity Level \".concat(usedLevel),\n                    type: 'success',\n                    details: \"Classified and routed to complexity level \".concat(usedLevel)\n                };\n            } else {\n                return {\n                    text: \"Complexity \".concat(classifiedLevel, \"→\").concat(usedLevel),\n                    type: 'success',\n                    details: \"Classified as level \".concat(classifiedLevel, \", routed to available level \").concat(usedLevel)\n                };\n            }\n        }\n        // Simple pattern: complexity_level_3\n        const levelMatch = roleUsed.match(/complexity_level_(\\d+)/);\n        if (levelMatch) {\n            const level = levelMatch[1];\n            return {\n                text: \"Complexity Level \".concat(level),\n                type: 'success',\n                details: \"Routed based on prompt complexity analysis\"\n            };\n        }\n    }\n    // Handle strict fallback\n    if (roleUsed.includes('fallback_position_')) {\n        const posMatch = roleUsed.match(/fallback_position_(\\d+)/);\n        const position = posMatch ? parseInt(posMatch[1]) : 0;\n        return {\n            text: \"Fallback Key #\".concat(position + 1),\n            type: 'success',\n            details: \"Used fallback key at position \".concat(position + 1)\n        };\n    }\n    // Handle intelligent role routing\n    if (roleUsed.includes('intelligent_role_')) {\n        const roleMatch = roleUsed.match(/intelligent_role_(.+)$/);\n        const detectedRole = roleMatch ? roleMatch[1] : 'unknown';\n        return {\n            text: \"Smart: \".concat(formatRoleName(detectedRole)),\n            type: 'role',\n            details: \"AI detected role: \".concat(formatRoleName(detectedRole))\n        };\n    }\n    // Enhanced fallback: Extract meaningful information from any unrecognized pattern\n    return extractMeaningfulInfo(roleUsed);\n};\n/**\n * Extract meaningful information from unrecognized role_used patterns\n */ const extractMeaningfulInfo = (roleUsed)=>{\n    // Try to extract complexity information\n    const complexityMatch = roleUsed.match(/complexity[_\\s]*(\\d+)/i);\n    if (complexityMatch) {\n        const level = complexityMatch[1];\n        return {\n            text: \"Complexity Level \".concat(level),\n            type: 'success',\n            details: \"Extracted complexity level \".concat(level, \" from routing pattern\")\n        };\n    }\n    // Try to extract role names from complex patterns\n    const roleNameMatch = extractRoleFromPattern(roleUsed);\n    if (roleNameMatch) {\n        return {\n            text: formatRoleName(roleNameMatch),\n            type: 'role',\n            details: \"Extracted role: \".concat(formatRoleName(roleNameMatch))\n        };\n    }\n    // Try to extract fallback information\n    const fallbackMatch = roleUsed.match(/fallback[_\\s]*(\\d+)/i);\n    if (fallbackMatch) {\n        const position = parseInt(fallbackMatch[1]);\n        return {\n            text: \"Fallback Key #\".concat(position + 1),\n            type: 'success',\n            details: \"Extracted fallback position \".concat(position + 1)\n        };\n    }\n    // Try to extract attempt information\n    const attemptMatch = roleUsed.match(/attempt[_\\s]*(\\d+)/i);\n    if (attemptMatch) {\n        const attempt = parseInt(attemptMatch[1]);\n        const isSuccess = roleUsed.toLowerCase().includes('success');\n        const isFailed = roleUsed.toLowerCase().includes('fail');\n        if (isSuccess) {\n            return {\n                text: attempt === 1 ? 'Default Key' : \"Default Key (Attempt \".concat(attempt, \")\"),\n                type: 'success',\n                details: \"Extracted success on attempt \".concat(attempt)\n            };\n        } else if (isFailed) {\n            return {\n                text: \"Failed (Attempt \".concat(attempt, \")\"),\n                type: 'error',\n                details: \"Extracted failure on attempt \".concat(attempt)\n            };\n        }\n    }\n    // Last resort: try to clean up the raw string for display\n    const cleanedText = cleanRawRoleUsed(roleUsed);\n    return {\n        text: cleanedText,\n        type: 'fallback',\n        details: \"Raw routing pattern: \".concat(roleUsed)\n    };\n};\n/**\n * Extract role names from complex patterns\n */ const extractRoleFromPattern = (str)=>{\n    // Look for patterns like \"classified_as_ROLENAME_something\"\n    const classifiedMatch = str.match(/classified_as_([a-z_]+)_/i);\n    if (classifiedMatch && isLikelyRoleName(classifiedMatch[1])) {\n        return classifiedMatch[1];\n    }\n    // Look for patterns like \"role_ROLENAME_something\"\n    const roleMatch = str.match(/role_([a-z_]+)_/i);\n    if (roleMatch && isLikelyRoleName(roleMatch[1])) {\n        return roleMatch[1];\n    }\n    // Look for patterns like \"fb_role_ROLENAME\"\n    const fbRoleMatch = str.match(/fb_role_([a-z_]+)/i);\n    if (fbRoleMatch && isLikelyRoleName(fbRoleMatch[1])) {\n        return fbRoleMatch[1];\n    }\n    return null;\n};\n/**\n * Clean up raw role_used strings for display as last resort\n */ const cleanRawRoleUsed = (str)=>{\n    // Remove common technical prefixes/suffixes\n    const cleaned = str.replace(/^default_key_[a-f0-9-]+_/i, '').replace(/_attempt_\\d+$/i, '').replace(/_status_\\w+$/i, '').replace(/_key_selected$/i, '').replace(/_then_.*$/i, '').replace(/^complexity_rr_/i, '').replace(/_no_.*$/i, '');\n    // If we cleaned it down to something reasonable, format it\n    if (cleaned && cleaned.length > 2 && cleaned.length < 30 && isLikelyRoleName(cleaned)) {\n        return formatRoleName(cleaned);\n    }\n    // Otherwise, just clean up the original string minimally\n    return str.replace(/_/g, ' ').replace(/([a-z])([A-Z])/g, '$1 $2').split(' ').map((word)=>word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' ').substring(0, 30) + (str.length > 30 ? '...' : '');\n};\n/**\n * Convert snake_case role names to Title Case\n */ const formatRoleName = (roleName)=>{\n    return roleName.split('_').map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(' ');\n};\n/**\n * Get CSS classes for role used badges based on type\n */ const getRoleUsedBadgeClass = (type)=>{\n    switch(type){\n        case 'role':\n            return 'inline-block px-2 py-1 rounded text-xs font-medium bg-blue-500/15 text-blue-300 border border-blue-500/25';\n        case 'success':\n            return 'inline-block px-2 py-1 rounded text-xs font-medium bg-green-500/15 text-green-300 border border-green-500/25';\n        case 'error':\n            return 'inline-block px-2 py-1 rounded text-xs font-medium bg-red-500/15 text-red-300 border border-red-500/25';\n        case 'fallback':\n        default:\n            return 'inline-block px-2 py-1 rounded text-xs font-medium bg-orange-500/15 text-orange-300 border border-orange-500/25';\n    }\n};\n/**\n * Generate production-ready role_used strings for logging\n */ const generateRoleUsedMessage = {\n    // Default routing messages\n    defaultKeySuccess: function() {\n        let attempt = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return attempt === 1 ? 'default_key_success' : \"default_key_success_attempt_\".concat(attempt);\n    },\n    defaultKeyFailed: function() {\n        let attempt = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, status = arguments.length > 1 ? arguments[1] : void 0;\n        return \"default_key_failed\".concat(status ? \"_status_\".concat(status) : '', \"_attempt_\").concat(attempt);\n    },\n    allKeysFailed: (attemptCount)=>\"default_all_\".concat(attemptCount, \"_attempts_failed\"),\n    // Role-based routing messages\n    roleRouting: (roleName)=>roleName,\n    intelligentRoleRouting: (detectedRole)=>\"intelligent_role_\".concat(detectedRole),\n    // Complexity-based routing messages\n    complexityRouting: (level, keyIndex)=>keyIndex !== undefined ? \"complexity_level_\".concat(level, \"_key_\").concat(keyIndex) : \"complexity_level_\".concat(level),\n    // Strict fallback routing messages\n    fallbackRouting: (position)=>\"fallback_position_\".concat(position),\n    // Error states\n    noKeysAvailable: ()=>'no_keys_available',\n    configurationError: ()=>'configuration_error',\n    routingStrategyError: (strategy)=>\"routing_strategy_error_\".concat(strategy)\n};\n/**\n * Transform provider names to user-friendly display names\n */ const formatProviderName = (provider)=>{\n    if (!provider) return 'N/A';\n    const providerMap = {\n        'openai': 'OpenAI',\n        'anthropic': 'Anthropic',\n        'google': 'Google',\n        'openrouter': 'OpenRouter',\n        'deepseek': 'DeepSeek',\n        'xai': 'xAI'\n    };\n    return providerMap[provider.toLowerCase()] || provider;\n};\n/**\n * Transform model names to user-friendly display names\n */ const formatModelName = (modelName)=>{\n    if (!modelName) return 'N/A';\n    // Remove common prefixes and make more readable\n    return modelName.replace(/^(gpt-|claude-|gemini-|meta-llama\\/|deepseek-|grok-)/, '').replace(/-/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase());\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy91dGlscy9sb2dGb3JtYXR0aW5nLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBOztDQUVDLEdBUUQ7O0NBRUMsR0FDRCxNQUFNQSxtQkFBbUIsQ0FBQ0M7SUFDeEIscUNBQXFDO0lBQ3JDLE1BQU1DLG9CQUFvQjtRQUN4QjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVELDREQUE0RDtJQUM1RCxJQUFJQSxrQkFBa0JDLElBQUksQ0FBQ0MsQ0FBQUEsVUFBV0EsUUFBUUMsSUFBSSxDQUFDSixPQUFPO1FBQ3hELE9BQU87SUFDVDtJQUVBLHFGQUFxRjtJQUNyRixPQUFPLGFBQWFJLElBQUksQ0FBQ0osUUFBUUEsSUFBSUssTUFBTSxHQUFHLEtBQUtMLElBQUlLLE1BQU0sR0FBRztBQUNsRTtBQUVBOztDQUVDLEdBQ00sTUFBTUMsb0JBQW9CLENBQUNDO0lBQ2hDLElBQUksQ0FBQ0EsVUFBVSxPQUFPO1FBQUVDLE1BQU07UUFBT0MsTUFBTTtJQUFXO0lBRXRELCtFQUErRTtJQUMvRSxJQUFJVixpQkFBaUJRLFdBQVc7UUFDOUIsT0FBTztZQUNMQyxNQUFNRSxlQUFlSDtZQUNyQkUsTUFBTTtZQUNORSxTQUFTLHVCQUFnRCxPQUF6QkQsZUFBZUg7UUFDakQ7SUFDRjtJQUVBLHNDQUFzQztJQUN0QyxJQUFJQSxTQUFTSyxRQUFRLENBQUMsa0JBQWtCTCxTQUFTSyxRQUFRLENBQUMsWUFBWTtRQUNwRSxNQUFNQyxlQUFlTixTQUFTTyxLQUFLLENBQUM7UUFDcEMsTUFBTUMsVUFBVUYsZUFBZUcsU0FBU0gsWUFBWSxDQUFDLEVBQUUsSUFBSTtRQUMzRCxPQUFPO1lBQ0xMLE1BQU1PLFlBQVksSUFBSSxnQkFBZ0Isd0JBQWdDLE9BQVJBLFNBQVE7WUFDdEVOLE1BQU07WUFDTkUsU0FBU0ksVUFBVSxJQUFJLFlBQW9CLE9BQVJBLFNBQVEsMEJBQXdCRTtRQUNyRTtJQUNGO0lBRUEsc0NBQXNDO0lBQ3RDLElBQUlWLFNBQVNLLFFBQVEsQ0FBQyxrQkFBa0JMLFNBQVNLLFFBQVEsQ0FBQyxXQUFXO1FBQ25FLE1BQU1DLGVBQWVOLFNBQVNPLEtBQUssQ0FBQztRQUNwQyxNQUFNSSxjQUFjWCxTQUFTTyxLQUFLLENBQUM7UUFDbkMsTUFBTUMsVUFBVUYsZUFBZUcsU0FBU0gsWUFBWSxDQUFDLEVBQUUsSUFBSTtRQUMzRCxNQUFNTSxTQUFTRCxjQUFjQSxXQUFXLENBQUMsRUFBRSxHQUFHO1FBQzlDLE9BQU87WUFDTFYsTUFBTSxtQkFBMkIsT0FBUk8sU0FBUTtZQUNqQ04sTUFBTTtZQUNORSxTQUFTLHVCQUE4QixPQUFQUTtRQUNsQztJQUNGO0lBRUEsa0NBQWtDO0lBQ2xDLElBQUlaLFNBQVNLLFFBQVEsQ0FBQyxrQkFBa0JMLFNBQVNLLFFBQVEsQ0FBQyxvQkFBb0I7UUFDNUUsTUFBTVEsYUFBYWIsU0FBU08sS0FBSyxDQUFDO1FBQ2xDLE1BQU1PLFFBQVFELGFBQWFKLFNBQVNJLFVBQVUsQ0FBQyxFQUFFLElBQUk7UUFDckQsT0FBTztZQUNMWixNQUFNLG9CQUEwQixPQUFOYSxPQUFNO1lBQ2hDWixNQUFNO1lBQ05FLFNBQVMsU0FBZSxPQUFOVSxPQUFNO1FBQzFCO0lBQ0Y7SUFFQSx3RUFBd0U7SUFDeEUsSUFBSWQsU0FBU0ssUUFBUSxDQUFDLDBCQUEwQkwsU0FBU0ssUUFBUSxDQUFDLHNCQUFzQjtRQUN0RixpRUFBaUU7UUFDakUsTUFBTVUsZ0JBQWdCZixTQUFTTyxLQUFLLENBQUM7UUFDckMsSUFBSVEsZUFBZTtZQUNqQixNQUFNQyxrQkFBa0JELGFBQWEsQ0FBQyxFQUFFO1lBQ3hDLE1BQU1FLFlBQVlGLGFBQWEsQ0FBQyxFQUFFO1lBQ2xDLElBQUlDLG9CQUFvQkMsV0FBVztnQkFDakMsT0FBTztvQkFDTGhCLE1BQU0sb0JBQThCLE9BQVZnQjtvQkFDMUJmLE1BQU07b0JBQ05FLFNBQVMsNkNBQXVELE9BQVZhO2dCQUN4RDtZQUNGLE9BQU87Z0JBQ0wsT0FBTztvQkFDTGhCLE1BQU0sY0FBaUNnQixPQUFuQkQsaUJBQWdCLEtBQWEsT0FBVkM7b0JBQ3ZDZixNQUFNO29CQUNORSxTQUFTLHVCQUFxRWEsT0FBOUNELGlCQUFnQixnQ0FBd0MsT0FBVkM7Z0JBQ2hGO1lBQ0Y7UUFDRjtRQUVBLHFDQUFxQztRQUNyQyxNQUFNQyxhQUFhbEIsU0FBU08sS0FBSyxDQUFDO1FBQ2xDLElBQUlXLFlBQVk7WUFDZCxNQUFNQyxRQUFRRCxVQUFVLENBQUMsRUFBRTtZQUMzQixPQUFPO2dCQUNMakIsTUFBTSxvQkFBMEIsT0FBTmtCO2dCQUMxQmpCLE1BQU07Z0JBQ05FLFNBQVU7WUFDWjtRQUNGO0lBQ0Y7SUFFQSx5QkFBeUI7SUFDekIsSUFBSUosU0FBU0ssUUFBUSxDQUFDLHVCQUF1QjtRQUMzQyxNQUFNZSxXQUFXcEIsU0FBU08sS0FBSyxDQUFDO1FBQ2hDLE1BQU1jLFdBQVdELFdBQVdYLFNBQVNXLFFBQVEsQ0FBQyxFQUFFLElBQUk7UUFDcEQsT0FBTztZQUNMbkIsTUFBTSxpQkFBOEIsT0FBYm9CLFdBQVc7WUFDbENuQixNQUFNO1lBQ05FLFNBQVMsaUNBQThDLE9BQWJpQixXQUFXO1FBQ3ZEO0lBQ0Y7SUFFQSxrQ0FBa0M7SUFDbEMsSUFBSXJCLFNBQVNLLFFBQVEsQ0FBQyxzQkFBc0I7UUFDMUMsTUFBTWlCLFlBQVl0QixTQUFTTyxLQUFLLENBQUM7UUFDakMsTUFBTWdCLGVBQWVELFlBQVlBLFNBQVMsQ0FBQyxFQUFFLEdBQUc7UUFDaEQsT0FBTztZQUNMckIsTUFBTSxVQUF1QyxPQUE3QkUsZUFBZW9CO1lBQy9CckIsTUFBTTtZQUNORSxTQUFTLHFCQUFrRCxPQUE3QkQsZUFBZW9CO1FBQy9DO0lBQ0Y7SUFFQSxrRkFBa0Y7SUFDbEYsT0FBT0Msc0JBQXNCeEI7QUFDL0IsRUFBRTtBQUVGOztDQUVDLEdBQ0QsTUFBTXdCLHdCQUF3QixDQUFDeEI7SUFDN0Isd0NBQXdDO0lBQ3hDLE1BQU15QixrQkFBa0J6QixTQUFTTyxLQUFLLENBQUM7SUFDdkMsSUFBSWtCLGlCQUFpQjtRQUNuQixNQUFNTixRQUFRTSxlQUFlLENBQUMsRUFBRTtRQUNoQyxPQUFPO1lBQ0x4QixNQUFNLG9CQUEwQixPQUFOa0I7WUFDMUJqQixNQUFNO1lBQ05FLFNBQVMsOEJBQW9DLE9BQU5lLE9BQU07UUFDL0M7SUFDRjtJQUVBLGtEQUFrRDtJQUNsRCxNQUFNTyxnQkFBZ0JDLHVCQUF1QjNCO0lBQzdDLElBQUkwQixlQUFlO1FBQ2pCLE9BQU87WUFDTHpCLE1BQU1FLGVBQWV1QjtZQUNyQnhCLE1BQU07WUFDTkUsU0FBUyxtQkFBaUQsT0FBOUJELGVBQWV1QjtRQUM3QztJQUNGO0lBRUEsc0NBQXNDO0lBQ3RDLE1BQU1FLGdCQUFnQjVCLFNBQVNPLEtBQUssQ0FBQztJQUNyQyxJQUFJcUIsZUFBZTtRQUNqQixNQUFNUCxXQUFXWixTQUFTbUIsYUFBYSxDQUFDLEVBQUU7UUFDMUMsT0FBTztZQUNMM0IsTUFBTSxpQkFBOEIsT0FBYm9CLFdBQVc7WUFDbENuQixNQUFNO1lBQ05FLFNBQVMsK0JBQTRDLE9BQWJpQixXQUFXO1FBQ3JEO0lBQ0Y7SUFFQSxxQ0FBcUM7SUFDckMsTUFBTWYsZUFBZU4sU0FBU08sS0FBSyxDQUFDO0lBQ3BDLElBQUlELGNBQWM7UUFDaEIsTUFBTUUsVUFBVUMsU0FBU0gsWUFBWSxDQUFDLEVBQUU7UUFDeEMsTUFBTXVCLFlBQVk3QixTQUFTOEIsV0FBVyxHQUFHekIsUUFBUSxDQUFDO1FBQ2xELE1BQU0wQixXQUFXL0IsU0FBUzhCLFdBQVcsR0FBR3pCLFFBQVEsQ0FBQztRQUVqRCxJQUFJd0IsV0FBVztZQUNiLE9BQU87Z0JBQ0w1QixNQUFNTyxZQUFZLElBQUksZ0JBQWdCLHdCQUFnQyxPQUFSQSxTQUFRO2dCQUN0RU4sTUFBTTtnQkFDTkUsU0FBUyxnQ0FBd0MsT0FBUkk7WUFDM0M7UUFDRixPQUFPLElBQUl1QixVQUFVO1lBQ25CLE9BQU87Z0JBQ0w5QixNQUFNLG1CQUEyQixPQUFSTyxTQUFRO2dCQUNqQ04sTUFBTTtnQkFDTkUsU0FBUyxnQ0FBd0MsT0FBUkk7WUFDM0M7UUFDRjtJQUNGO0lBRUEsMERBQTBEO0lBQzFELE1BQU13QixjQUFjQyxpQkFBaUJqQztJQUNyQyxPQUFPO1FBQ0xDLE1BQU0rQjtRQUNOOUIsTUFBTTtRQUNORSxTQUFTLHdCQUFpQyxPQUFUSjtJQUNuQztBQUNGO0FBRUE7O0NBRUMsR0FDRCxNQUFNMkIseUJBQXlCLENBQUNsQztJQUM5Qiw0REFBNEQ7SUFDNUQsTUFBTXlDLGtCQUFrQnpDLElBQUljLEtBQUssQ0FBQztJQUNsQyxJQUFJMkIsbUJBQW1CMUMsaUJBQWlCMEMsZUFBZSxDQUFDLEVBQUUsR0FBRztRQUMzRCxPQUFPQSxlQUFlLENBQUMsRUFBRTtJQUMzQjtJQUVBLG1EQUFtRDtJQUNuRCxNQUFNWixZQUFZN0IsSUFBSWMsS0FBSyxDQUFDO0lBQzVCLElBQUllLGFBQWE5QixpQkFBaUI4QixTQUFTLENBQUMsRUFBRSxHQUFHO1FBQy9DLE9BQU9BLFNBQVMsQ0FBQyxFQUFFO0lBQ3JCO0lBRUEsNENBQTRDO0lBQzVDLE1BQU1hLGNBQWMxQyxJQUFJYyxLQUFLLENBQUM7SUFDOUIsSUFBSTRCLGVBQWUzQyxpQkFBaUIyQyxXQUFXLENBQUMsRUFBRSxHQUFHO1FBQ25ELE9BQU9BLFdBQVcsQ0FBQyxFQUFFO0lBQ3ZCO0lBRUEsT0FBTztBQUNUO0FBRUE7O0NBRUMsR0FDRCxNQUFNRixtQkFBbUIsQ0FBQ3hDO0lBQ3hCLDRDQUE0QztJQUM1QyxNQUFNMkMsVUFBVTNDLElBQ2I0QyxPQUFPLENBQUMsNkJBQTZCLElBQ3JDQSxPQUFPLENBQUMsa0JBQWtCLElBQzFCQSxPQUFPLENBQUMsaUJBQWlCLElBQ3pCQSxPQUFPLENBQUMsbUJBQW1CLElBQzNCQSxPQUFPLENBQUMsY0FBYyxJQUN0QkEsT0FBTyxDQUFDLG9CQUFvQixJQUM1QkEsT0FBTyxDQUFDLFlBQVk7SUFFdkIsMkRBQTJEO0lBQzNELElBQUlELFdBQVdBLFFBQVF0QyxNQUFNLEdBQUcsS0FBS3NDLFFBQVF0QyxNQUFNLEdBQUcsTUFBTU4saUJBQWlCNEMsVUFBVTtRQUNyRixPQUFPakMsZUFBZWlDO0lBQ3hCO0lBRUEseURBQXlEO0lBQ3pELE9BQU8zQyxJQUNKNEMsT0FBTyxDQUFDLE1BQU0sS0FDZEEsT0FBTyxDQUFDLG1CQUFtQixTQUMzQkMsS0FBSyxDQUFDLEtBQ05DLEdBQUcsQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS0MsTUFBTSxDQUFDLEdBQUdDLFdBQVcsS0FBS0YsS0FBS0csS0FBSyxDQUFDLEdBQUdiLFdBQVcsSUFDcEVjLElBQUksQ0FBQyxLQUNMQyxTQUFTLENBQUMsR0FBRyxNQUFPcEQsQ0FBQUEsSUFBSUssTUFBTSxHQUFHLEtBQUssUUFBUSxFQUFDO0FBQ3BEO0FBRUE7O0NBRUMsR0FDTSxNQUFNSyxpQkFBaUIsQ0FBQzJDO0lBQzdCLE9BQU9BLFNBQ0pSLEtBQUssQ0FBQyxLQUNOQyxHQUFHLENBQUNDLENBQUFBLE9BQVFBLEtBQUtDLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUtGLEtBQUtHLEtBQUssQ0FBQyxJQUN0REMsSUFBSSxDQUFDO0FBQ1YsRUFBRTtBQUVGOztDQUVDLEdBQ00sTUFBTUcsd0JBQXdCLENBQUM3QztJQUNwQyxPQUFRQTtRQUNOLEtBQUs7WUFDSCxPQUFPO1FBQ1QsS0FBSztZQUNILE9BQU87UUFDVCxLQUFLO1lBQ0gsT0FBTztRQUNULEtBQUs7UUFDTDtZQUNFLE9BQU87SUFDWDtBQUNGLEVBQUU7QUFFRjs7Q0FFQyxHQUNNLE1BQU04QywwQkFBMEI7SUFDckMsMkJBQTJCO0lBQzNCQyxtQkFBbUI7WUFBQ3pDLDJFQUFrQjtlQUNwQ0EsWUFBWSxJQUFJLHdCQUF3QiwrQkFBdUMsT0FBUkE7O0lBRXpFMEMsa0JBQWtCO1lBQUMxQywyRUFBa0IsR0FBR0k7ZUFDdEMscUJBQWtFSixPQUE3Q0ksU0FBUyxXQUFrQixPQUFQQSxVQUFXLElBQUcsYUFBbUIsT0FBUko7O0lBRXBFMkMsZUFBZSxDQUFDQyxlQUNkLGVBQTRCLE9BQWJBLGNBQWE7SUFFOUIsOEJBQThCO0lBQzlCQyxhQUFhLENBQUNQLFdBQXFCQTtJQUVuQ1Esd0JBQXdCLENBQUMvQixlQUN2QixvQkFBaUMsT0FBYkE7SUFFdEIsb0NBQW9DO0lBQ3BDZ0MsbUJBQW1CLENBQUNwQyxPQUFlcUMsV0FDakNBLGFBQWE5QyxZQUNULG9CQUFpQzhDLE9BQWJyQyxPQUFNLFNBQWdCLE9BQVRxQyxZQUNqQyxvQkFBMEIsT0FBTnJDO0lBRTFCLG1DQUFtQztJQUNuQ3NDLGlCQUFpQixDQUFDcEMsV0FDaEIscUJBQThCLE9BQVRBO0lBRXZCLGVBQWU7SUFDZnFDLGlCQUFpQixJQUFNO0lBQ3ZCQyxvQkFBb0IsSUFBTTtJQUMxQkMsc0JBQXNCLENBQUNDLFdBQXFCLDBCQUFtQyxPQUFUQTtBQUN4RSxFQUFFO0FBRUY7O0NBRUMsR0FDTSxNQUFNQyxxQkFBcUIsQ0FBQ0M7SUFDakMsSUFBSSxDQUFDQSxVQUFVLE9BQU87SUFFdEIsTUFBTUMsY0FBc0M7UUFDMUMsVUFBVTtRQUNWLGFBQWE7UUFDYixVQUFVO1FBQ1YsY0FBYztRQUNkLFlBQVk7UUFDWixPQUFPO0lBQ1Q7SUFFQSxPQUFPQSxXQUFXLENBQUNELFNBQVNqQyxXQUFXLEdBQUcsSUFBSWlDO0FBQ2hELEVBQUU7QUFFRjs7Q0FFQyxHQUNNLE1BQU1FLGtCQUFrQixDQUFDQztJQUM5QixJQUFJLENBQUNBLFdBQVcsT0FBTztJQUV2QixnREFBZ0Q7SUFDaEQsT0FBT0EsVUFDSjdCLE9BQU8sQ0FBQyx3REFBd0QsSUFDaEVBLE9BQU8sQ0FBQyxNQUFNLEtBQ2RBLE9BQU8sQ0FBQyxTQUFTOEIsQ0FBQUEsSUFBS0EsRUFBRXpCLFdBQVc7QUFDeEMsRUFBRSIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXHV0aWxzXFxsb2dGb3JtYXR0aW5nLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVXRpbGl0eSBmdW5jdGlvbnMgZm9yIHRyYW5zZm9ybWluZyBkZWJ1Zy1zdHlsZSBsb2cgbWVzc2FnZXMgaW50byBwcm9kdWN0aW9uLXJlYWR5LCB1c2VyLWZyaWVuZGx5IHRleHRcbiAqL1xuXG5leHBvcnQgaW50ZXJmYWNlIFJvbGVVc2VkSW5mbyB7XG4gIHRleHQ6IHN0cmluZztcbiAgdHlwZTogJ3N1Y2Nlc3MnIHwgJ2Vycm9yJyB8ICdmYWxsYmFjaycgfCAncm9sZSc7XG4gIGRldGFpbHM/OiBzdHJpbmc7XG59XG5cbi8qKlxuICogQ2hlY2sgaWYgYSBzdHJpbmcgbG9va3MgbGlrZSBhIHJvbGUgbmFtZSAodnMgYSB0ZWNobmljYWwgZGVidWcgcGF0dGVybilcbiAqL1xuY29uc3QgaXNMaWtlbHlSb2xlTmFtZSA9IChzdHI6IHN0cmluZyk6IGJvb2xlYW4gPT4ge1xuICAvLyBFeGNsdWRlIG9idmlvdXMgdGVjaG5pY2FsIHBhdHRlcm5zXG4gIGNvbnN0IHRlY2huaWNhbFBhdHRlcm5zID0gW1xuICAgIC9kZWZhdWx0X2tleS9pLFxuICAgIC9hdHRlbXB0X1xcZCsvaSxcbiAgICAvc3RhdHVzX1xcZCsvaSxcbiAgICAvZmFpbGVkL2ksXG4gICAgL3N1Y2Nlc3MvaSxcbiAgICAvY29tcGxleGl0eV9yci9pLFxuICAgIC9mYWxsYmFja19wb3NpdGlvbi9pLFxuICAgIC9eW2EtZjAtOS1dezgsfS9pLCAvLyBVVUlEcyBvciBsb25nIGhleCBzdHJpbmdzXG4gICAgL190aGVuXy9pLFxuICAgIC9jbGFzc2lmaWNhdGlvbl8vaSxcbiAgICAvbm9fcHJvbXB0L2ksXG4gICAgL2Vycm9yL2lcbiAgXTtcbiAgXG4gIC8vIElmIGl0IG1hdGNoZXMgYW55IHRlY2huaWNhbCBwYXR0ZXJuLCBpdCdzIG5vdCBhIHJvbGUgbmFtZVxuICBpZiAodGVjaG5pY2FsUGF0dGVybnMuc29tZShwYXR0ZXJuID0+IHBhdHRlcm4udGVzdChzdHIpKSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBcbiAgLy8gSWYgaXQncyBhIHNpbXBsZSB3b3JkIG9yIHNuYWtlX2Nhc2Ugd2l0aG91dCBudW1iZXJzL3RlY2huaWNhbCB0ZXJtcywgbGlrZWx5IGEgcm9sZVxuICByZXR1cm4gL15bYS16X10rJC9pLnRlc3Qoc3RyKSAmJiBzdHIubGVuZ3RoID4gMiAmJiBzdHIubGVuZ3RoIDwgNTA7XG59O1xuXG4vKipcbiAqIFRyYW5zZm9ybSBkZWJ1Zy1zdHlsZSByb2xlX3VzZWQgbWVzc2FnZXMgaW50byB1c2VyLWZyaWVuZGx5IHRleHRcbiAqL1xuZXhwb3J0IGNvbnN0IHRyYW5zZm9ybVJvbGVVc2VkID0gKHJvbGVVc2VkOiBzdHJpbmcgfCBudWxsKTogUm9sZVVzZWRJbmZvID0+IHtcbiAgaWYgKCFyb2xlVXNlZCkgcmV0dXJuIHsgdGV4dDogJ04vQScsIHR5cGU6ICdmYWxsYmFjaycgfTtcbiAgXG4gIC8vIEhhbmRsZSBzaW1wbGUgcm9sZSBuYW1lcyBmaXJzdCAoY2xlYW4gcm9sZSBuYW1lcyB3aXRob3V0IHRlY2huaWNhbCBwYXR0ZXJucylcbiAgaWYgKGlzTGlrZWx5Um9sZU5hbWUocm9sZVVzZWQpKSB7XG4gICAgcmV0dXJuIHsgXG4gICAgICB0ZXh0OiBmb3JtYXRSb2xlTmFtZShyb2xlVXNlZCksIFxuICAgICAgdHlwZTogJ3JvbGUnLFxuICAgICAgZGV0YWlsczogYFJvbGUtYmFzZWQgcm91dGluZzogJHtmb3JtYXRSb2xlTmFtZShyb2xlVXNlZCl9YFxuICAgIH07XG4gIH1cbiAgXG4gIC8vIEhhbmRsZSBkZWZhdWx0IGtleSBzdWNjZXNzIHBhdHRlcm5zXG4gIGlmIChyb2xlVXNlZC5pbmNsdWRlcygnZGVmYXVsdF9rZXknKSAmJiByb2xlVXNlZC5pbmNsdWRlcygnc3VjY2VzcycpKSB7XG4gICAgY29uc3QgYXR0ZW1wdE1hdGNoID0gcm9sZVVzZWQubWF0Y2goL2F0dGVtcHRfKFxcZCspLyk7XG4gICAgY29uc3QgYXR0ZW1wdCA9IGF0dGVtcHRNYXRjaCA/IHBhcnNlSW50KGF0dGVtcHRNYXRjaFsxXSkgOiAxO1xuICAgIHJldHVybiB7IFxuICAgICAgdGV4dDogYXR0ZW1wdCA9PT0gMSA/ICdEZWZhdWx0IEtleScgOiBgRGVmYXVsdCBLZXkgKEF0dGVtcHQgJHthdHRlbXB0fSlgLCBcbiAgICAgIHR5cGU6ICdzdWNjZXNzJyxcbiAgICAgIGRldGFpbHM6IGF0dGVtcHQgPiAxID8gYFJlcXVpcmVkICR7YXR0ZW1wdH0gYXR0ZW1wdHMgdG8gc3VjY2VlZGAgOiB1bmRlZmluZWRcbiAgICB9O1xuICB9XG4gIFxuICAvLyBIYW5kbGUgZGVmYXVsdCBrZXkgZmFpbHVyZSBwYXR0ZXJuc1xuICBpZiAocm9sZVVzZWQuaW5jbHVkZXMoJ2RlZmF1bHRfa2V5JykgJiYgcm9sZVVzZWQuaW5jbHVkZXMoJ2ZhaWxlZCcpKSB7XG4gICAgY29uc3QgYXR0ZW1wdE1hdGNoID0gcm9sZVVzZWQubWF0Y2goL2F0dGVtcHRfKFxcZCspLyk7XG4gICAgY29uc3Qgc3RhdHVzTWF0Y2ggPSByb2xlVXNlZC5tYXRjaCgvc3RhdHVzXyhcXHcrKS8pO1xuICAgIGNvbnN0IGF0dGVtcHQgPSBhdHRlbXB0TWF0Y2ggPyBwYXJzZUludChhdHRlbXB0TWF0Y2hbMV0pIDogMTtcbiAgICBjb25zdCBzdGF0dXMgPSBzdGF0dXNNYXRjaCA/IHN0YXR1c01hdGNoWzFdIDogJ3Vua25vd24nO1xuICAgIHJldHVybiB7IFxuICAgICAgdGV4dDogYEZhaWxlZCAoQXR0ZW1wdCAke2F0dGVtcHR9KWAsIFxuICAgICAgdHlwZTogJ2Vycm9yJyxcbiAgICAgIGRldGFpbHM6IGBGYWlsZWQgd2l0aCBzdGF0dXM6ICR7c3RhdHVzfWBcbiAgICB9O1xuICB9XG4gIFxuICAvLyBIYW5kbGUgbXVsdGlwbGUgYXR0ZW1wdHMgZmFpbGVkXG4gIGlmIChyb2xlVXNlZC5pbmNsdWRlcygnZGVmYXVsdF9hbGwnKSAmJiByb2xlVXNlZC5pbmNsdWRlcygnYXR0ZW1wdHNfZmFpbGVkJykpIHtcbiAgICBjb25zdCBjb3VudE1hdGNoID0gcm9sZVVzZWQubWF0Y2goL2RlZmF1bHRfYWxsXyhcXGQrKV9hdHRlbXB0cy8pO1xuICAgIGNvbnN0IGNvdW50ID0gY291bnRNYXRjaCA/IHBhcnNlSW50KGNvdW50TWF0Y2hbMV0pIDogMDtcbiAgICByZXR1cm4geyBcbiAgICAgIHRleHQ6IGBBbGwgS2V5cyBGYWlsZWQgKCR7Y291bnR9IGF0dGVtcHRzKWAsIFxuICAgICAgdHlwZTogJ2Vycm9yJyxcbiAgICAgIGRldGFpbHM6IGBUcmllZCAke2NvdW50fSBkaWZmZXJlbnQgQVBJIGtleXMsIGFsbCBmYWlsZWRgXG4gICAgfTtcbiAgfVxuICBcbiAgLy8gSGFuZGxlIGVuaGFuY2VkIGNvbXBsZXhpdHktYmFzZWQgcm91dGluZyB3aXRoIHByb3hpbWFsIHNlYXJjaCBkZXRhaWxzXG4gIGlmIChyb2xlVXNlZC5pbmNsdWRlcygnY29tcGxleGl0eV9ycl9jbHNmXycpIHx8IHJvbGVVc2VkLmluY2x1ZGVzKCdjb21wbGV4aXR5X2xldmVsXycpKSB7XG4gICAgLy8gRW5oYW5jZWQgcGF0dGVybjogY29tcGxleGl0eV9ycl9jbHNmXzNfdXNlZF9sdmxfNF9rZXlfc2VsZWN0ZWRcbiAgICBjb25zdCBlbmhhbmNlZE1hdGNoID0gcm9sZVVzZWQubWF0Y2goL2NvbXBsZXhpdHlfcnJfY2xzZl8oXFxkKylfdXNlZF9sdmxfKFxcZCspLyk7XG4gICAgaWYgKGVuaGFuY2VkTWF0Y2gpIHtcbiAgICAgIGNvbnN0IGNsYXNzaWZpZWRMZXZlbCA9IGVuaGFuY2VkTWF0Y2hbMV07XG4gICAgICBjb25zdCB1c2VkTGV2ZWwgPSBlbmhhbmNlZE1hdGNoWzJdO1xuICAgICAgaWYgKGNsYXNzaWZpZWRMZXZlbCA9PT0gdXNlZExldmVsKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgdGV4dDogYENvbXBsZXhpdHkgTGV2ZWwgJHt1c2VkTGV2ZWx9YCxcbiAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsXG4gICAgICAgICAgZGV0YWlsczogYENsYXNzaWZpZWQgYW5kIHJvdXRlZCB0byBjb21wbGV4aXR5IGxldmVsICR7dXNlZExldmVsfWBcbiAgICAgICAgfTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgdGV4dDogYENvbXBsZXhpdHkgJHtjbGFzc2lmaWVkTGV2ZWx94oaSJHt1c2VkTGV2ZWx9YCxcbiAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsXG4gICAgICAgICAgZGV0YWlsczogYENsYXNzaWZpZWQgYXMgbGV2ZWwgJHtjbGFzc2lmaWVkTGV2ZWx9LCByb3V0ZWQgdG8gYXZhaWxhYmxlIGxldmVsICR7dXNlZExldmVsfWBcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICB9XG4gICAgXG4gICAgLy8gU2ltcGxlIHBhdHRlcm46IGNvbXBsZXhpdHlfbGV2ZWxfM1xuICAgIGNvbnN0IGxldmVsTWF0Y2ggPSByb2xlVXNlZC5tYXRjaCgvY29tcGxleGl0eV9sZXZlbF8oXFxkKykvKTtcbiAgICBpZiAobGV2ZWxNYXRjaCkge1xuICAgICAgY29uc3QgbGV2ZWwgPSBsZXZlbE1hdGNoWzFdO1xuICAgICAgcmV0dXJuIHsgXG4gICAgICAgIHRleHQ6IGBDb21wbGV4aXR5IExldmVsICR7bGV2ZWx9YCwgXG4gICAgICAgIHR5cGU6ICdzdWNjZXNzJyxcbiAgICAgICAgZGV0YWlsczogYFJvdXRlZCBiYXNlZCBvbiBwcm9tcHQgY29tcGxleGl0eSBhbmFseXNpc2BcbiAgICAgIH07XG4gICAgfVxuICB9XG4gIFxuICAvLyBIYW5kbGUgc3RyaWN0IGZhbGxiYWNrXG4gIGlmIChyb2xlVXNlZC5pbmNsdWRlcygnZmFsbGJhY2tfcG9zaXRpb25fJykpIHtcbiAgICBjb25zdCBwb3NNYXRjaCA9IHJvbGVVc2VkLm1hdGNoKC9mYWxsYmFja19wb3NpdGlvbl8oXFxkKykvKTtcbiAgICBjb25zdCBwb3NpdGlvbiA9IHBvc01hdGNoID8gcGFyc2VJbnQocG9zTWF0Y2hbMV0pIDogMDtcbiAgICByZXR1cm4geyBcbiAgICAgIHRleHQ6IGBGYWxsYmFjayBLZXkgIyR7cG9zaXRpb24gKyAxfWAsIFxuICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgZGV0YWlsczogYFVzZWQgZmFsbGJhY2sga2V5IGF0IHBvc2l0aW9uICR7cG9zaXRpb24gKyAxfWBcbiAgICB9O1xuICB9XG4gIFxuICAvLyBIYW5kbGUgaW50ZWxsaWdlbnQgcm9sZSByb3V0aW5nXG4gIGlmIChyb2xlVXNlZC5pbmNsdWRlcygnaW50ZWxsaWdlbnRfcm9sZV8nKSkge1xuICAgIGNvbnN0IHJvbGVNYXRjaCA9IHJvbGVVc2VkLm1hdGNoKC9pbnRlbGxpZ2VudF9yb2xlXyguKykkLyk7XG4gICAgY29uc3QgZGV0ZWN0ZWRSb2xlID0gcm9sZU1hdGNoID8gcm9sZU1hdGNoWzFdIDogJ3Vua25vd24nO1xuICAgIHJldHVybiB7XG4gICAgICB0ZXh0OiBgU21hcnQ6ICR7Zm9ybWF0Um9sZU5hbWUoZGV0ZWN0ZWRSb2xlKX1gLFxuICAgICAgdHlwZTogJ3JvbGUnLFxuICAgICAgZGV0YWlsczogYEFJIGRldGVjdGVkIHJvbGU6ICR7Zm9ybWF0Um9sZU5hbWUoZGV0ZWN0ZWRSb2xlKX1gXG4gICAgfTtcbiAgfVxuICBcbiAgLy8gRW5oYW5jZWQgZmFsbGJhY2s6IEV4dHJhY3QgbWVhbmluZ2Z1bCBpbmZvcm1hdGlvbiBmcm9tIGFueSB1bnJlY29nbml6ZWQgcGF0dGVyblxuICByZXR1cm4gZXh0cmFjdE1lYW5pbmdmdWxJbmZvKHJvbGVVc2VkKTtcbn07XG5cbi8qKlxuICogRXh0cmFjdCBtZWFuaW5nZnVsIGluZm9ybWF0aW9uIGZyb20gdW5yZWNvZ25pemVkIHJvbGVfdXNlZCBwYXR0ZXJuc1xuICovXG5jb25zdCBleHRyYWN0TWVhbmluZ2Z1bEluZm8gPSAocm9sZVVzZWQ6IHN0cmluZyk6IFJvbGVVc2VkSW5mbyA9PiB7XG4gIC8vIFRyeSB0byBleHRyYWN0IGNvbXBsZXhpdHkgaW5mb3JtYXRpb25cbiAgY29uc3QgY29tcGxleGl0eU1hdGNoID0gcm9sZVVzZWQubWF0Y2goL2NvbXBsZXhpdHlbX1xcc10qKFxcZCspL2kpO1xuICBpZiAoY29tcGxleGl0eU1hdGNoKSB7XG4gICAgY29uc3QgbGV2ZWwgPSBjb21wbGV4aXR5TWF0Y2hbMV07XG4gICAgcmV0dXJuIHtcbiAgICAgIHRleHQ6IGBDb21wbGV4aXR5IExldmVsICR7bGV2ZWx9YCxcbiAgICAgIHR5cGU6ICdzdWNjZXNzJyxcbiAgICAgIGRldGFpbHM6IGBFeHRyYWN0ZWQgY29tcGxleGl0eSBsZXZlbCAke2xldmVsfSBmcm9tIHJvdXRpbmcgcGF0dGVybmBcbiAgICB9O1xuICB9XG5cbiAgLy8gVHJ5IHRvIGV4dHJhY3Qgcm9sZSBuYW1lcyBmcm9tIGNvbXBsZXggcGF0dGVybnNcbiAgY29uc3Qgcm9sZU5hbWVNYXRjaCA9IGV4dHJhY3RSb2xlRnJvbVBhdHRlcm4ocm9sZVVzZWQpO1xuICBpZiAocm9sZU5hbWVNYXRjaCkge1xuICAgIHJldHVybiB7XG4gICAgICB0ZXh0OiBmb3JtYXRSb2xlTmFtZShyb2xlTmFtZU1hdGNoKSxcbiAgICAgIHR5cGU6ICdyb2xlJyxcbiAgICAgIGRldGFpbHM6IGBFeHRyYWN0ZWQgcm9sZTogJHtmb3JtYXRSb2xlTmFtZShyb2xlTmFtZU1hdGNoKX1gXG4gICAgfTtcbiAgfVxuXG4gIC8vIFRyeSB0byBleHRyYWN0IGZhbGxiYWNrIGluZm9ybWF0aW9uXG4gIGNvbnN0IGZhbGxiYWNrTWF0Y2ggPSByb2xlVXNlZC5tYXRjaCgvZmFsbGJhY2tbX1xcc10qKFxcZCspL2kpO1xuICBpZiAoZmFsbGJhY2tNYXRjaCkge1xuICAgIGNvbnN0IHBvc2l0aW9uID0gcGFyc2VJbnQoZmFsbGJhY2tNYXRjaFsxXSk7XG4gICAgcmV0dXJuIHtcbiAgICAgIHRleHQ6IGBGYWxsYmFjayBLZXkgIyR7cG9zaXRpb24gKyAxfWAsXG4gICAgICB0eXBlOiAnc3VjY2VzcycsXG4gICAgICBkZXRhaWxzOiBgRXh0cmFjdGVkIGZhbGxiYWNrIHBvc2l0aW9uICR7cG9zaXRpb24gKyAxfWBcbiAgICB9O1xuICB9XG5cbiAgLy8gVHJ5IHRvIGV4dHJhY3QgYXR0ZW1wdCBpbmZvcm1hdGlvblxuICBjb25zdCBhdHRlbXB0TWF0Y2ggPSByb2xlVXNlZC5tYXRjaCgvYXR0ZW1wdFtfXFxzXSooXFxkKykvaSk7XG4gIGlmIChhdHRlbXB0TWF0Y2gpIHtcbiAgICBjb25zdCBhdHRlbXB0ID0gcGFyc2VJbnQoYXR0ZW1wdE1hdGNoWzFdKTtcbiAgICBjb25zdCBpc1N1Y2Nlc3MgPSByb2xlVXNlZC50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCdzdWNjZXNzJyk7XG4gICAgY29uc3QgaXNGYWlsZWQgPSByb2xlVXNlZC50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCdmYWlsJyk7XG5cbiAgICBpZiAoaXNTdWNjZXNzKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICB0ZXh0OiBhdHRlbXB0ID09PSAxID8gJ0RlZmF1bHQgS2V5JyA6IGBEZWZhdWx0IEtleSAoQXR0ZW1wdCAke2F0dGVtcHR9KWAsXG4gICAgICAgIHR5cGU6ICdzdWNjZXNzJyxcbiAgICAgICAgZGV0YWlsczogYEV4dHJhY3RlZCBzdWNjZXNzIG9uIGF0dGVtcHQgJHthdHRlbXB0fWBcbiAgICAgIH07XG4gICAgfSBlbHNlIGlmIChpc0ZhaWxlZCkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgdGV4dDogYEZhaWxlZCAoQXR0ZW1wdCAke2F0dGVtcHR9KWAsXG4gICAgICAgIHR5cGU6ICdlcnJvcicsXG4gICAgICAgIGRldGFpbHM6IGBFeHRyYWN0ZWQgZmFpbHVyZSBvbiBhdHRlbXB0ICR7YXR0ZW1wdH1gXG4gICAgICB9O1xuICAgIH1cbiAgfVxuXG4gIC8vIExhc3QgcmVzb3J0OiB0cnkgdG8gY2xlYW4gdXAgdGhlIHJhdyBzdHJpbmcgZm9yIGRpc3BsYXlcbiAgY29uc3QgY2xlYW5lZFRleHQgPSBjbGVhblJhd1JvbGVVc2VkKHJvbGVVc2VkKTtcbiAgcmV0dXJuIHtcbiAgICB0ZXh0OiBjbGVhbmVkVGV4dCxcbiAgICB0eXBlOiAnZmFsbGJhY2snLFxuICAgIGRldGFpbHM6IGBSYXcgcm91dGluZyBwYXR0ZXJuOiAke3JvbGVVc2VkfWBcbiAgfTtcbn07XG5cbi8qKlxuICogRXh0cmFjdCByb2xlIG5hbWVzIGZyb20gY29tcGxleCBwYXR0ZXJuc1xuICovXG5jb25zdCBleHRyYWN0Um9sZUZyb21QYXR0ZXJuID0gKHN0cjogc3RyaW5nKTogc3RyaW5nIHwgbnVsbCA9PiB7XG4gIC8vIExvb2sgZm9yIHBhdHRlcm5zIGxpa2UgXCJjbGFzc2lmaWVkX2FzX1JPTEVOQU1FX3NvbWV0aGluZ1wiXG4gIGNvbnN0IGNsYXNzaWZpZWRNYXRjaCA9IHN0ci5tYXRjaCgvY2xhc3NpZmllZF9hc18oW2Etel9dKylfL2kpO1xuICBpZiAoY2xhc3NpZmllZE1hdGNoICYmIGlzTGlrZWx5Um9sZU5hbWUoY2xhc3NpZmllZE1hdGNoWzFdKSkge1xuICAgIHJldHVybiBjbGFzc2lmaWVkTWF0Y2hbMV07XG4gIH1cblxuICAvLyBMb29rIGZvciBwYXR0ZXJucyBsaWtlIFwicm9sZV9ST0xFTkFNRV9zb21ldGhpbmdcIlxuICBjb25zdCByb2xlTWF0Y2ggPSBzdHIubWF0Y2goL3JvbGVfKFthLXpfXSspXy9pKTtcbiAgaWYgKHJvbGVNYXRjaCAmJiBpc0xpa2VseVJvbGVOYW1lKHJvbGVNYXRjaFsxXSkpIHtcbiAgICByZXR1cm4gcm9sZU1hdGNoWzFdO1xuICB9XG5cbiAgLy8gTG9vayBmb3IgcGF0dGVybnMgbGlrZSBcImZiX3JvbGVfUk9MRU5BTUVcIlxuICBjb25zdCBmYlJvbGVNYXRjaCA9IHN0ci5tYXRjaCgvZmJfcm9sZV8oW2Etel9dKykvaSk7XG4gIGlmIChmYlJvbGVNYXRjaCAmJiBpc0xpa2VseVJvbGVOYW1lKGZiUm9sZU1hdGNoWzFdKSkge1xuICAgIHJldHVybiBmYlJvbGVNYXRjaFsxXTtcbiAgfVxuXG4gIHJldHVybiBudWxsO1xufTtcblxuLyoqXG4gKiBDbGVhbiB1cCByYXcgcm9sZV91c2VkIHN0cmluZ3MgZm9yIGRpc3BsYXkgYXMgbGFzdCByZXNvcnRcbiAqL1xuY29uc3QgY2xlYW5SYXdSb2xlVXNlZCA9IChzdHI6IHN0cmluZyk6IHN0cmluZyA9PiB7XG4gIC8vIFJlbW92ZSBjb21tb24gdGVjaG5pY2FsIHByZWZpeGVzL3N1ZmZpeGVzXG4gIGNvbnN0IGNsZWFuZWQgPSBzdHJcbiAgICAucmVwbGFjZSgvXmRlZmF1bHRfa2V5X1thLWYwLTktXStfL2ksICcnKVxuICAgIC5yZXBsYWNlKC9fYXR0ZW1wdF9cXGQrJC9pLCAnJylcbiAgICAucmVwbGFjZSgvX3N0YXR1c19cXHcrJC9pLCAnJylcbiAgICAucmVwbGFjZSgvX2tleV9zZWxlY3RlZCQvaSwgJycpXG4gICAgLnJlcGxhY2UoL190aGVuXy4qJC9pLCAnJylcbiAgICAucmVwbGFjZSgvXmNvbXBsZXhpdHlfcnJfL2ksICcnKVxuICAgIC5yZXBsYWNlKC9fbm9fLiokL2ksICcnKTtcblxuICAvLyBJZiB3ZSBjbGVhbmVkIGl0IGRvd24gdG8gc29tZXRoaW5nIHJlYXNvbmFibGUsIGZvcm1hdCBpdFxuICBpZiAoY2xlYW5lZCAmJiBjbGVhbmVkLmxlbmd0aCA+IDIgJiYgY2xlYW5lZC5sZW5ndGggPCAzMCAmJiBpc0xpa2VseVJvbGVOYW1lKGNsZWFuZWQpKSB7XG4gICAgcmV0dXJuIGZvcm1hdFJvbGVOYW1lKGNsZWFuZWQpO1xuICB9XG5cbiAgLy8gT3RoZXJ3aXNlLCBqdXN0IGNsZWFuIHVwIHRoZSBvcmlnaW5hbCBzdHJpbmcgbWluaW1hbGx5XG4gIHJldHVybiBzdHJcbiAgICAucmVwbGFjZSgvXy9nLCAnICcpXG4gICAgLnJlcGxhY2UoLyhbYS16XSkoW0EtWl0pL2csICckMSAkMicpXG4gICAgLnNwbGl0KCcgJylcbiAgICAubWFwKHdvcmQgPT4gd29yZC5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIHdvcmQuc2xpY2UoMSkudG9Mb3dlckNhc2UoKSlcbiAgICAuam9pbignICcpXG4gICAgLnN1YnN0cmluZygwLCAzMCkgKyAoc3RyLmxlbmd0aCA+IDMwID8gJy4uLicgOiAnJyk7XG59O1xuXG4vKipcbiAqIENvbnZlcnQgc25ha2VfY2FzZSByb2xlIG5hbWVzIHRvIFRpdGxlIENhc2VcbiAqL1xuZXhwb3J0IGNvbnN0IGZvcm1hdFJvbGVOYW1lID0gKHJvbGVOYW1lOiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICByZXR1cm4gcm9sZU5hbWVcbiAgICAuc3BsaXQoJ18nKVxuICAgIC5tYXAod29yZCA9PiB3b3JkLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgd29yZC5zbGljZSgxKSlcbiAgICAuam9pbignICcpO1xufTtcblxuLyoqXG4gKiBHZXQgQ1NTIGNsYXNzZXMgZm9yIHJvbGUgdXNlZCBiYWRnZXMgYmFzZWQgb24gdHlwZVxuICovXG5leHBvcnQgY29uc3QgZ2V0Um9sZVVzZWRCYWRnZUNsYXNzID0gKHR5cGU6ICdzdWNjZXNzJyB8ICdlcnJvcicgfCAnZmFsbGJhY2snIHwgJ3JvbGUnKTogc3RyaW5nID0+IHtcbiAgc3dpdGNoICh0eXBlKSB7XG4gICAgY2FzZSAncm9sZSc6XG4gICAgICByZXR1cm4gJ2lubGluZS1ibG9jayBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLWJsdWUtNTAwLzE1IHRleHQtYmx1ZS0zMDAgYm9yZGVyIGJvcmRlci1ibHVlLTUwMC8yNSc7XG4gICAgY2FzZSAnc3VjY2Vzcyc6XG4gICAgICByZXR1cm4gJ2lubGluZS1ibG9jayBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLWdyZWVuLTUwMC8xNSB0ZXh0LWdyZWVuLTMwMCBib3JkZXIgYm9yZGVyLWdyZWVuLTUwMC8yNSc7XG4gICAgY2FzZSAnZXJyb3InOlxuICAgICAgcmV0dXJuICdpbmxpbmUtYmxvY2sgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC14cyBmb250LW1lZGl1bSBiZy1yZWQtNTAwLzE1IHRleHQtcmVkLTMwMCBib3JkZXIgYm9yZGVyLXJlZC01MDAvMjUnO1xuICAgIGNhc2UgJ2ZhbGxiYWNrJzpcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuICdpbmxpbmUtYmxvY2sgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC14cyBmb250LW1lZGl1bSBiZy1vcmFuZ2UtNTAwLzE1IHRleHQtb3JhbmdlLTMwMCBib3JkZXIgYm9yZGVyLW9yYW5nZS01MDAvMjUnO1xuICB9XG59O1xuXG4vKipcbiAqIEdlbmVyYXRlIHByb2R1Y3Rpb24tcmVhZHkgcm9sZV91c2VkIHN0cmluZ3MgZm9yIGxvZ2dpbmdcbiAqL1xuZXhwb3J0IGNvbnN0IGdlbmVyYXRlUm9sZVVzZWRNZXNzYWdlID0ge1xuICAvLyBEZWZhdWx0IHJvdXRpbmcgbWVzc2FnZXNcbiAgZGVmYXVsdEtleVN1Y2Nlc3M6IChhdHRlbXB0OiBudW1iZXIgPSAxKSA9PlxuICAgIGF0dGVtcHQgPT09IDEgPyAnZGVmYXVsdF9rZXlfc3VjY2VzcycgOiBgZGVmYXVsdF9rZXlfc3VjY2Vzc19hdHRlbXB0XyR7YXR0ZW1wdH1gLFxuXG4gIGRlZmF1bHRLZXlGYWlsZWQ6IChhdHRlbXB0OiBudW1iZXIgPSAxLCBzdGF0dXM/OiBzdHJpbmcgfCBudW1iZXIpID0+XG4gICAgYGRlZmF1bHRfa2V5X2ZhaWxlZCR7c3RhdHVzID8gYF9zdGF0dXNfJHtzdGF0dXN9YCA6ICcnfV9hdHRlbXB0XyR7YXR0ZW1wdH1gLFxuXG4gIGFsbEtleXNGYWlsZWQ6IChhdHRlbXB0Q291bnQ6IG51bWJlcikgPT5cbiAgICBgZGVmYXVsdF9hbGxfJHthdHRlbXB0Q291bnR9X2F0dGVtcHRzX2ZhaWxlZGAsXG5cbiAgLy8gUm9sZS1iYXNlZCByb3V0aW5nIG1lc3NhZ2VzXG4gIHJvbGVSb3V0aW5nOiAocm9sZU5hbWU6IHN0cmluZykgPT4gcm9sZU5hbWUsXG5cbiAgaW50ZWxsaWdlbnRSb2xlUm91dGluZzogKGRldGVjdGVkUm9sZTogc3RyaW5nKSA9PlxuICAgIGBpbnRlbGxpZ2VudF9yb2xlXyR7ZGV0ZWN0ZWRSb2xlfWAsXG5cbiAgLy8gQ29tcGxleGl0eS1iYXNlZCByb3V0aW5nIG1lc3NhZ2VzXG4gIGNvbXBsZXhpdHlSb3V0aW5nOiAobGV2ZWw6IG51bWJlciwga2V5SW5kZXg/OiBudW1iZXIpID0+XG4gICAga2V5SW5kZXggIT09IHVuZGVmaW5lZFxuICAgICAgPyBgY29tcGxleGl0eV9sZXZlbF8ke2xldmVsfV9rZXlfJHtrZXlJbmRleH1gXG4gICAgICA6IGBjb21wbGV4aXR5X2xldmVsXyR7bGV2ZWx9YCxcblxuICAvLyBTdHJpY3QgZmFsbGJhY2sgcm91dGluZyBtZXNzYWdlc1xuICBmYWxsYmFja1JvdXRpbmc6IChwb3NpdGlvbjogbnVtYmVyKSA9PlxuICAgIGBmYWxsYmFja19wb3NpdGlvbl8ke3Bvc2l0aW9ufWAsXG5cbiAgLy8gRXJyb3Igc3RhdGVzXG4gIG5vS2V5c0F2YWlsYWJsZTogKCkgPT4gJ25vX2tleXNfYXZhaWxhYmxlJyxcbiAgY29uZmlndXJhdGlvbkVycm9yOiAoKSA9PiAnY29uZmlndXJhdGlvbl9lcnJvcicsXG4gIHJvdXRpbmdTdHJhdGVneUVycm9yOiAoc3RyYXRlZ3k6IHN0cmluZykgPT4gYHJvdXRpbmdfc3RyYXRlZ3lfZXJyb3JfJHtzdHJhdGVneX1gLFxufTtcblxuLyoqXG4gKiBUcmFuc2Zvcm0gcHJvdmlkZXIgbmFtZXMgdG8gdXNlci1mcmllbmRseSBkaXNwbGF5IG5hbWVzXG4gKi9cbmV4cG9ydCBjb25zdCBmb3JtYXRQcm92aWRlck5hbWUgPSAocHJvdmlkZXI6IHN0cmluZyB8IG51bGwpOiBzdHJpbmcgPT4ge1xuICBpZiAoIXByb3ZpZGVyKSByZXR1cm4gJ04vQSc7XG5cbiAgY29uc3QgcHJvdmlkZXJNYXA6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XG4gICAgJ29wZW5haSc6ICdPcGVuQUknLFxuICAgICdhbnRocm9waWMnOiAnQW50aHJvcGljJyxcbiAgICAnZ29vZ2xlJzogJ0dvb2dsZScsXG4gICAgJ29wZW5yb3V0ZXInOiAnT3BlblJvdXRlcicsXG4gICAgJ2RlZXBzZWVrJzogJ0RlZXBTZWVrJyxcbiAgICAneGFpJzogJ3hBSScsXG4gIH07XG5cbiAgcmV0dXJuIHByb3ZpZGVyTWFwW3Byb3ZpZGVyLnRvTG93ZXJDYXNlKCldIHx8IHByb3ZpZGVyO1xufTtcblxuLyoqXG4gKiBUcmFuc2Zvcm0gbW9kZWwgbmFtZXMgdG8gdXNlci1mcmllbmRseSBkaXNwbGF5IG5hbWVzXG4gKi9cbmV4cG9ydCBjb25zdCBmb3JtYXRNb2RlbE5hbWUgPSAobW9kZWxOYW1lOiBzdHJpbmcgfCBudWxsKTogc3RyaW5nID0+IHtcbiAgaWYgKCFtb2RlbE5hbWUpIHJldHVybiAnTi9BJztcblxuICAvLyBSZW1vdmUgY29tbW9uIHByZWZpeGVzIGFuZCBtYWtlIG1vcmUgcmVhZGFibGVcbiAgcmV0dXJuIG1vZGVsTmFtZVxuICAgIC5yZXBsYWNlKC9eKGdwdC18Y2xhdWRlLXxnZW1pbmktfG1ldGEtbGxhbWFcXC98ZGVlcHNlZWstfGdyb2stKS8sICcnKVxuICAgIC5yZXBsYWNlKC8tL2csICcgJylcbiAgICAucmVwbGFjZSgvXFxiXFx3L2csIGwgPT4gbC50b1VwcGVyQ2FzZSgpKTtcbn07XG4iXSwibmFtZXMiOlsiaXNMaWtlbHlSb2xlTmFtZSIsInN0ciIsInRlY2huaWNhbFBhdHRlcm5zIiwic29tZSIsInBhdHRlcm4iLCJ0ZXN0IiwibGVuZ3RoIiwidHJhbnNmb3JtUm9sZVVzZWQiLCJyb2xlVXNlZCIsInRleHQiLCJ0eXBlIiwiZm9ybWF0Um9sZU5hbWUiLCJkZXRhaWxzIiwiaW5jbHVkZXMiLCJhdHRlbXB0TWF0Y2giLCJtYXRjaCIsImF0dGVtcHQiLCJwYXJzZUludCIsInVuZGVmaW5lZCIsInN0YXR1c01hdGNoIiwic3RhdHVzIiwiY291bnRNYXRjaCIsImNvdW50IiwiZW5oYW5jZWRNYXRjaCIsImNsYXNzaWZpZWRMZXZlbCIsInVzZWRMZXZlbCIsImxldmVsTWF0Y2giLCJsZXZlbCIsInBvc01hdGNoIiwicG9zaXRpb24iLCJyb2xlTWF0Y2giLCJkZXRlY3RlZFJvbGUiLCJleHRyYWN0TWVhbmluZ2Z1bEluZm8iLCJjb21wbGV4aXR5TWF0Y2giLCJyb2xlTmFtZU1hdGNoIiwiZXh0cmFjdFJvbGVGcm9tUGF0dGVybiIsImZhbGxiYWNrTWF0Y2giLCJpc1N1Y2Nlc3MiLCJ0b0xvd2VyQ2FzZSIsImlzRmFpbGVkIiwiY2xlYW5lZFRleHQiLCJjbGVhblJhd1JvbGVVc2VkIiwiY2xhc3NpZmllZE1hdGNoIiwiZmJSb2xlTWF0Y2giLCJjbGVhbmVkIiwicmVwbGFjZSIsInNwbGl0IiwibWFwIiwid29yZCIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJqb2luIiwic3Vic3RyaW5nIiwicm9sZU5hbWUiLCJnZXRSb2xlVXNlZEJhZGdlQ2xhc3MiLCJnZW5lcmF0ZVJvbGVVc2VkTWVzc2FnZSIsImRlZmF1bHRLZXlTdWNjZXNzIiwiZGVmYXVsdEtleUZhaWxlZCIsImFsbEtleXNGYWlsZWQiLCJhdHRlbXB0Q291bnQiLCJyb2xlUm91dGluZyIsImludGVsbGlnZW50Um9sZVJvdXRpbmciLCJjb21wbGV4aXR5Um91dGluZyIsImtleUluZGV4IiwiZmFsbGJhY2tSb3V0aW5nIiwibm9LZXlzQXZhaWxhYmxlIiwiY29uZmlndXJhdGlvbkVycm9yIiwicm91dGluZ1N0cmF0ZWd5RXJyb3IiLCJzdHJhdGVneSIsImZvcm1hdFByb3ZpZGVyTmFtZSIsInByb3ZpZGVyIiwicHJvdmlkZXJNYXAiLCJmb3JtYXRNb2RlbE5hbWUiLCJtb2RlbE5hbWUiLCJsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/logFormatting.ts\n"));

/***/ })

});